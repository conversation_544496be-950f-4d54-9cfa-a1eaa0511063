#!/usr/bin/env python3
"""
Sanity check script to load CSV data and display calculated MD5 values
using the same MD5 calculation method from Airflow code.
"""

import csv
import hashlib
import json
import argparse
from pathlib import Path


def generate_md5_from_array(data_values):
    """
    Generate MD5 hash from array of values using the same method as Airflow code.

    Args:
        data_values (list): An array containing items of various types.

    Returns:
        str: The MD5 hash of the concatenated string in hexadecimal format.
    """
    data_str = json.dumps(data_values, sort_keys=True, default=str)
    return hashlib.md5(data_str.encode()).hexdigest()


def load_and_process_csv(csv_file_path, limit=10):
    """
    Load CSV file and calculate MD5 values for each row.

    Args:
        csv_file_path (str): Path to the CSV file
        limit (int): Maximum number of rows to process (default: 10)

    Returns:
        list: List of dictionaries containing row data and calculated MD5
    """
    results = []

    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)

            # Get column names (excluding potential md5_hash and dv_load_timestamp columns)
            data_columns = [col for col in reader.fieldnames
                           if col not in ['md5_hash', 'dv_load_timestamp']]

            print(f"Data columns found: {data_columns}")
            print(f"Processing up to {limit} rows...\n")

            for i, row in enumerate(reader):
                if i >= limit:
                    break

                # Extract data values in the same order as columns
                data_values = [row.get(col) for col in data_columns]

                # Debug: show original data types and values
                print(f"Original data_values: {data_values}")
                print(f"Data types: {[type(val).__name__ for val in data_values]}")

                # Convert feedback_score to integer to match API data type
                if 'feedback_score' in data_columns:
                    score_idx = data_columns.index('feedback_score')
                    if data_values[score_idx] is not None:
                        data_values[score_idx] = int(data_values[score_idx])

                print(f"After conversion: {data_values}")
                print(f"Data types after: {[type(val).__name__ for val in data_values]}")

                # Calculate MD5 hash using the same method as Airflow
                md5_hash = generate_md5_from_array(data_values)

                # Store result
                result = {
                    'row_number': i + 1,
                    'data_values': data_values,
                    'data_columns': data_columns,
                    'md5_hash': md5_hash,
                    'original_row': dict(row)
                }
                results.append(result)

    except FileNotFoundError:
        print(f"Error: File '{csv_file_path}' not found.")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []

    return results


def display_results(results):
    """
    Display the calculated MD5 values and related information.

    Args:
        results (list): List of result dictionaries
    """
    if not results:
        print("No results to display.")
        return

    print("=" * 80)
    print("MD5 CALCULATION RESULTS")
    print("=" * 80)

    for result in results:
        print(f"\nRow {result['row_number']}:")
        print(f"  Data Values: {result['data_values']}")
        print(f"  MD5 Hash: {result['md5_hash']}")
        print(f"  Original Row: {result['original_row']}")
        print("-" * 60)

    print(f"\nProcessed {len(results)} rows total.")


def main():
    """Main function to run the sanity check."""
    parser = argparse.ArgumentParser(description='Sanity check MD5 calculation for CSV data')
    parser.add_argument('--csv-file',
                       default='feedback_dataset.csv',
                       help='Path to CSV file (default: feedback_dataset.csv)')
    parser.add_argument('--limit',
                       type=int,
                       default=10,
                       help='Number of rows to process (default: 10)')

    args = parser.parse_args()

    # Resolve file path
    csv_path = Path(args.csv_file)
    if not csv_path.is_absolute():
        csv_path = Path(__file__).parent / csv_path

    print(f"Loading CSV file: {csv_path}")
    print(f"Processing limit: {args.limit}")
    print()

    # Load and process CSV
    results = load_and_process_csv(csv_path, args.limit)

    # Display results
    display_results(results)


if __name__ == "__main__":
    main()
